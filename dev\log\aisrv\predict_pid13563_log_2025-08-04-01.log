{"time": "2025-08-04 01:05:09.792160", "level": "INFO", "message": "aisrv policy_name train_one, algo ppo, model_wrapper is StandardModelWrapperPytorch", "file": "model_wrapper_common.py", "line": "99", "module": "aisrv", "process": "model_wrapper_common", "function": "create_standard_model_wrapper", "stack": "", "pid": 13563}
{"time": "2025-08-04 01:05:09.797023", "level": "INFO", "message": "aisrv predict just no model_pool files or copy_model_files failed, please check", "file": "multi_model_common.py", "line": "240", "module": "aisrv", "process": "multi_model_common", "function": "init_load_models", "stack": "", "pid": 13563}
{"time": "2025-08-04 01:05:09.799301", "level": "INFO", "message": "predict policy_name: train_one, start success at pid 13563, on-policy/off-policy is off-policy, actor_receive_cost_time_ms: 1, predict_batch_size: 1", "file": "actor_proxy_local.py", "line": "663", "module": "aisrv", "process": "actor_proxy_local", "function": "before_run", "stack": "", "pid": 13563}
{"time": "2025-08-04 01:05:09.805691", "level": "INFO", "message": "aisrv predict now predict count is 0", "file": "actor_proxy_local.py", "line": "482", "module": "aisrv", "process": "actor_proxy_local", "function": "predict_stat", "stack": "", "pid": 13563}
{"time": "2025-08-04 01:05:10.833712", "level": "INFO", "message": "aisrv monitor_proxy process start success at pid is 13613", "file": "monitor_proxy_process.py", "line": "73", "module": "aisrv", "process": "monitor_proxy_process", "function": "before_run", "stack": "", "pid": 13613}
{"time": "2025-08-04 01:05:12.444066", "level": "INFO", "message": "aisrv predict had not get pull model file, so return", "file": "predict_common.py", "line": "319", "module": "aisrv", "process": "predict_common", "function": "standard_load_model_detail", "stack": "", "pid": 13563}
