{"time": "2025-08-04 01:05:14.081338", "level": "INFO", "message": "aisrv learner_proxy reverb client Client, server_address=127.0.0.1:9999 127.0.0.1:9999 connect to reverb server", "file": "reverb_util.py", "line": "22", "module": "aisrv", "process": "reverb_util", "function": "__init__", "stack": "", "pid": 13564}
{"time": "2025-08-04 01:05:14.084346", "level": "INFO", "message": "learner_proxy send reverb server use reverb, tables is ['reverb_replay_buffer_table_0']", "file": "learner_proxy.py", "line": "118", "module": "aisrv", "process": "learner_proxy", "function": "before_run", "stack": "", "pid": 13564}
{"time": "2025-08-04 01:05:14.089247", "level": "INFO", "message": "learner_proxy zmq client connect at 127.0.0.1:9997 with client_id 94461035", "file": "learner_proxy.py", "line": "155", "module": "aisrv", "process": "learner_proxy", "function": "before_run", "stack": "", "pid": 13564}
{"time": "2025-08-04 01:05:14.131469", "level": "INFO", "message": "learner_proxy policy_name: train_one, start success at pid 13564", "file": "learner_proxy.py", "line": "174", "module": "aisrv", "process": "learner_proxy", "function": "before_run", "stack": "", "pid": 13564}
{"time": "2025-08-04 01:05:16.292504", "level": "INFO", "message": "learner_proxy send save_model data to learner", "file": "learner_proxy.py", "line": "253", "module": "aisrv", "process": "learner_proxy", "function": "run_once", "stack": "", "pid": 13564}
{"time": "2025-08-04 01:05:16.294079", "level": "INFO", "message": "learner_proxy recv save_model data result from learner success", "file": "learner_proxy.py", "line": "264", "module": "aisrv", "process": "learner_proxy", "function": "run_once", "stack": "", "pid": 13564}
