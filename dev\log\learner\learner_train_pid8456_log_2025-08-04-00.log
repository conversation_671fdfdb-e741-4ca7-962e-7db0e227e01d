{"time": "2025-08-04 00:45:44.487196", "level": "INFO", "message": "learner train process start at pid is 8456", "file": "trainer.py", "line": "32", "module": "learner", "process": "trainer", "function": "__init__", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:44.526251", "level": "INFO", "message": "learner train replaybuff, use reverb", "file": "replay_buffer_wrapper.py", "line": "58", "module": "learner", "process": "replay_buffer_wrapper", "function": "__init__", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:44.538241", "level": "INFO", "message": "learner model_file_save process start, type is 0, no need get mode file from cos", "file": "model_file_save.py", "line": "776", "module": "learner", "process": "model_file_save", "function": "start_actor_process_by_type", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:44.917231", "level": "INFO", "message": "learner monitor_proxy process start success at pid is 8572", "file": "monitor_proxy_process.py", "line": "73", "module": "learner", "process": "monitor_proxy_process", "function": "before_run", "stack": "", "pid": 8572}
{"time": "2025-08-04 00:45:45.472575", "level": "INFO", "message": "learner policy_name train_one, algo ppo, model_wrapper is StandardModelWrapperPytorch", "file": "model_wrapper_common.py", "line": "99", "module": "learner", "process": "model_wrapper_common", "function": "create_standard_model_wrapper", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:45.506013", "level": "INFO", "message": "learner train zmq server on learner bind at 0.0.0.0:9997 for aisrv", "file": "on_policy_trainer.py", "line": "691", "module": "learner", "process": "on_policy_trainer", "function": "before_run", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:45.516655", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-0.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:45.556873", "level": "INFO", "message": "model_file_sync push output_file_name /data/ckpt/back_to_the_realm_v2_ppo/kaiwu_checkpoint_back_to_the_realm_v2_ppo_0.tar.gz key model.ckpt_back_to_the_realm_v2_ppo_0 to modelpool success,             total push to modelpool succ cnt is 1             total push to modelpool err cnt is 0", "file": "model_file_sync.py", "line": "560", "module": "learner", "process": "model_file_sync", "function": "push_checkpoint_to_model_pool", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:45.557806", "level": "INFO", "message": "learner train first model file push to modelpool success", "file": "on_policy_trainer.py", "line": "723", "module": "learner", "process": "on_policy_trainer", "function": "before_run", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:45.560855", "level": "INFO", "message": "learner train process start success at 8456, on-policy/off-policy is off-policy, ppo trainer global step -1.0, load app back_to_the_realm_v2 algo ppo model, train_batch_size is 2", "file": "on_policy_trainer.py", "line": "756", "module": "learner", "process": "on_policy_trainer", "function": "before_run", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:45.639898", "level": "INFO", "message": "learner start_background_filler success, reverb.Client connect at localhost:9999", "file": "reverb_dataset_v1.py", "line": "57", "module": "learner", "process": "reverb_dataset_v1", "function": "start_background_filler", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:45.868040", "level": "INFO", "message": "learner model_file_sync, use modelpool, use_which_deep_learning_framework is pytorch", "file": "model_file_sync.py", "line": "189", "module": "learner", "process": "model_file_sync", "function": "before_run", "stack": "", "pid": 8626}
{"time": "2025-08-04 00:45:45.869822", "level": "INFO", "message": "model_file_sync process pid is 8626", "file": "model_file_sync.py", "line": "195", "module": "learner", "process": "model_file_sync", "function": "before_run", "stack": "", "pid": 8626}
{"time": "2025-08-04 00:45:45.873410", "level": "INFO", "message": "model_file_sync mkdir /data/ckpt/back_to_the_realm_v2_ppo/convert_models_learner success", "file": "model_file_sync.py", "line": "153", "module": "learner", "process": "model_file_sync", "function": "make_model_dirs", "stack": "", "pid": 8626}
{"time": "2025-08-04 00:45:45.875533", "level": "INFO", "message": "learner ppid is 133454836369216", "file": "monitor_proxy.py", "line": "36", "module": "learner", "process": "monitor_proxy", "function": "__init__", "stack": "", "pid": 8626}
{"time": "2025-08-04 00:45:46.263788", "level": "INFO", "message": "learner ppid is 133454836369216", "file": "monitor_proxy.py", "line": "36", "module": "learner", "process": "monitor_proxy", "function": "__init__", "stack": "", "pid": 8630}
{"time": "2025-08-04 00:45:46.271081", "level": "INFO", "message": "model_file_save process start success at pid 8630", "file": "model_file_save.py", "line": "300", "module": "learner", "process": "model_file_save", "function": "before_run", "stack": "", "pid": 8630}
{"time": "2025-08-04 00:45:58.739008", "level": "INFO", "message": "learner train_step is 0, so not save_model", "file": "standard_model_wrapper_pytorch.py", "line": "238", "module": "learner", "process": "standard_model_wrapper_pytorch", "function": "save_param_detail", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:58.740435", "level": "INFO", "message": "learner train learner save_param_by_framework is success, ip is 172.20.0.5_0", "file": "on_policy_trainer.py", "line": "974", "module": "learner", "process": "on_policy_trainer", "function": "save_model_detail", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:58.741982", "level": "INFO", "message": "learner train learner really save_model from aisrv 172.20.0.5_0", "file": "on_policy_trainer.py", "line": "1055", "module": "learner", "process": "on_policy_trainer", "function": "learner_process_message_by_aisrv", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.040321", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-1.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.057832", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-2.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.083078", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-3.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.101224", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-4.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.117315", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-5.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.141498", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-6.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.160499", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-7.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.177000", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-8.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.198233", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-9.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.219204", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-10.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.236745", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-11.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.254919", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-12.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.274866", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-13.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.291615", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-14.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.311455", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-15.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.328195", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-16.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.346311", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-17.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.364246", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-18.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.386990", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-19.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.408186", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-20.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.432263", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-21.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.457458", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-22.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.476642", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-23.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.501172", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-24.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.524485", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-25.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.543469", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-26.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.562176", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-27.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.580643", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-28.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.601615", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-29.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.623807", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-30.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.639216", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-31.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.661185", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-32.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.683152", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-33.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.701326", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-34.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.723474", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-35.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.742157", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-36.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.762504", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-37.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.779318", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-38.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.800901", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-39.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.831658", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-40.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.850140", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-41.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.870324", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-42.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.893051", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-43.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.914193", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-44.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.938099", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-45.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.961110", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-46.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
{"time": "2025-08-04 00:45:59.988744", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-47.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 8456}
