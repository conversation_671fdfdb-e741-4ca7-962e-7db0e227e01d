{"time": "2025-08-04 01:13:47.051068", "level": "INFO", "message": "learner train process start at pid is 15977", "file": "trainer.py", "line": "32", "module": "learner", "process": "trainer", "function": "__init__", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:13:47.093813", "level": "INFO", "message": "learner train replaybuff, use reverb", "file": "replay_buffer_wrapper.py", "line": "58", "module": "learner", "process": "replay_buffer_wrapper", "function": "__init__", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:13:47.109723", "level": "INFO", "message": "learner model_file_save process start, type is 0, no need get mode file from cos", "file": "model_file_save.py", "line": "776", "module": "learner", "process": "model_file_save", "function": "start_actor_process_by_type", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:13:47.468764", "level": "INFO", "message": "learner monitor_proxy process start success at pid is 16102", "file": "monitor_proxy_process.py", "line": "73", "module": "learner", "process": "monitor_proxy_process", "function": "before_run", "stack": "", "pid": 16102}
{"time": "2025-08-04 01:13:48.152738", "level": "INFO", "message": "learner policy_name train_one, algo ppo, model_wrapper is StandardModelWrapperPytorch", "file": "model_wrapper_common.py", "line": "99", "module": "learner", "process": "model_wrapper_common", "function": "create_standard_model_wrapper", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:13:48.217435", "level": "INFO", "message": "learner train zmq server on learner bind at 0.0.0.0:9997 for aisrv", "file": "on_policy_trainer.py", "line": "691", "module": "learner", "process": "on_policy_trainer", "function": "before_run", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:13:48.229622", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-0.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:13:48.281118", "level": "INFO", "message": "model_file_sync push output_file_name /data/ckpt/back_to_the_realm_v2_ppo/kaiwu_checkpoint_back_to_the_realm_v2_ppo_0.tar.gz key model.ckpt_back_to_the_realm_v2_ppo_0 to modelpool success,             total push to modelpool succ cnt is 1             total push to modelpool err cnt is 0", "file": "model_file_sync.py", "line": "560", "module": "learner", "process": "model_file_sync", "function": "push_checkpoint_to_model_pool", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:13:48.282046", "level": "INFO", "message": "learner train first model file push to modelpool success", "file": "on_policy_trainer.py", "line": "723", "module": "learner", "process": "on_policy_trainer", "function": "before_run", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:13:48.285533", "level": "INFO", "message": "learner train process start success at 15977, on-policy/off-policy is off-policy, ppo trainer global step -1.0, load app back_to_the_realm_v2 algo ppo model, train_batch_size is 2", "file": "on_policy_trainer.py", "line": "756", "module": "learner", "process": "on_policy_trainer", "function": "before_run", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:13:48.295492", "level": "INFO", "message": "learner start_background_filler success, reverb.Client connect at localhost:9999", "file": "reverb_dataset_v1.py", "line": "57", "module": "learner", "process": "reverb_dataset_v1", "function": "start_background_filler", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:13:48.566544", "level": "INFO", "message": "learner model_file_sync, use modelpool, use_which_deep_learning_framework is pytorch", "file": "model_file_sync.py", "line": "189", "module": "learner", "process": "model_file_sync", "function": "before_run", "stack": "", "pid": 16149}
{"time": "2025-08-04 01:13:48.568571", "level": "INFO", "message": "model_file_sync process pid is 16149", "file": "model_file_sync.py", "line": "195", "module": "learner", "process": "model_file_sync", "function": "before_run", "stack": "", "pid": 16149}
{"time": "2025-08-04 01:13:48.569723", "level": "INFO", "message": "model_file_sync mkdir /data/ckpt/back_to_the_realm_v2_ppo/convert_models_learner success", "file": "model_file_sync.py", "line": "153", "module": "learner", "process": "model_file_sync", "function": "make_model_dirs", "stack": "", "pid": 16149}
{"time": "2025-08-04 01:13:48.572097", "level": "INFO", "message": "learner ppid is 125177456150336", "file": "monitor_proxy.py", "line": "36", "module": "learner", "process": "monitor_proxy", "function": "__init__", "stack": "", "pid": 16149}
{"time": "2025-08-04 01:13:48.945775", "level": "INFO", "message": "learner ppid is 125177456150336", "file": "monitor_proxy.py", "line": "36", "module": "learner", "process": "monitor_proxy", "function": "__init__", "stack": "", "pid": 16154}
{"time": "2025-08-04 01:13:48.949606", "level": "INFO", "message": "model_file_save process start success at pid 16154", "file": "model_file_save.py", "line": "300", "module": "learner", "process": "model_file_save", "function": "before_run", "stack": "", "pid": 16154}
{"time": "2025-08-04 01:14:00.774218", "level": "INFO", "message": "learner train_step is 0, so not save_model", "file": "standard_model_wrapper_pytorch.py", "line": "238", "module": "learner", "process": "standard_model_wrapper_pytorch", "function": "save_param_detail", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:00.775424", "level": "INFO", "message": "learner train learner save_param_by_framework is success, ip is 172.20.0.5_0", "file": "on_policy_trainer.py", "line": "974", "module": "learner", "process": "on_policy_trainer", "function": "save_model_detail", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:00.776213", "level": "INFO", "message": "learner train learner really save_model from aisrv 172.20.0.5_0", "file": "on_policy_trainer.py", "line": "1055", "module": "learner", "process": "on_policy_trainer", "function": "learner_process_message_by_aisrv", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.129123", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-1.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.144865", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-2.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.169566", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-3.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.188232", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-4.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.206103", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-5.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.229107", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-6.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.248144", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-7.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.275088", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-8.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.293138", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-9.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.312390", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-10.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.327297", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-11.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.343707", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-12.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.369908", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-13.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.387636", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-14.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.404695", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-15.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.424091", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-16.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.441987", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-17.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.462244", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-18.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.477976", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-19.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.498798", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-20.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.519054", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-21.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.533331", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-22.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.548206", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-23.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.566316", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-24.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.589520", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-25.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.610100", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-26.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.631174", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-27.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.646115", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-28.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.660893", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-29.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.679259", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-30.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.697724", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-31.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.719764", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-32.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.740133", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-33.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.760649", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-34.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.778336", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-35.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.800313", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-36.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.813111", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-37.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.836991", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-38.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.861693", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-39.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.884466", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-40.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.909121", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-41.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.929885", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-42.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.948227", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-43.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.966501", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-44.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:01.990224", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-45.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:02.011717", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-46.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
{"time": "2025-08-04 01:14:02.030541", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-47.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 15977}
