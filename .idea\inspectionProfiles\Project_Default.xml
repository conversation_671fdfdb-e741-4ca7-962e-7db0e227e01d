<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyCompatibilityInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ourVersions">
        <value>
          <list size="2">
            <item index="0" class="java.lang.String" itemvalue="2.7" />
            <item index="1" class="java.lang.String" itemvalue="3.13" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="37">
            <item index="0" class="java.lang.String" itemvalue="traitlets" />
            <item index="1" class="java.lang.String" itemvalue="scipy" />
            <item index="2" class="java.lang.String" itemvalue="jupyter-core" />
            <item index="3" class="java.lang.String" itemvalue="tornado" />
            <item index="4" class="java.lang.String" itemvalue="parso" />
            <item index="5" class="java.lang.String" itemvalue="python-dateutil" />
            <item index="6" class="java.lang.String" itemvalue="nbformat" />
            <item index="7" class="java.lang.String" itemvalue="ipython" />
            <item index="8" class="java.lang.String" itemvalue="jupyter-client" />
            <item index="9" class="java.lang.String" itemvalue="MarkupSafe" />
            <item index="10" class="java.lang.String" itemvalue="numpy" />
            <item index="11" class="java.lang.String" itemvalue="ipykernel" />
            <item index="12" class="java.lang.String" itemvalue="Jinja2" />
            <item index="13" class="java.lang.String" itemvalue="nbconvert" />
            <item index="14" class="java.lang.String" itemvalue="attrs" />
            <item index="15" class="java.lang.String" itemvalue="mistune" />
            <item index="16" class="java.lang.String" itemvalue="jedi" />
            <item index="17" class="java.lang.String" itemvalue="Pygments" />
            <item index="18" class="java.lang.String" itemvalue="pyzmq" />
            <item index="19" class="java.lang.String" itemvalue="matplotlib" />
            <item index="20" class="java.lang.String" itemvalue="jsonschema" />
            <item index="21" class="java.lang.String" itemvalue="Pillow" />
            <item index="22" class="java.lang.String" itemvalue="onnxruntime-gpu" />
            <item index="23" class="java.lang.String" itemvalue="timm" />
            <item index="24" class="java.lang.String" itemvalue="gradio" />
            <item index="25" class="java.lang.String" itemvalue="opencv-python" />
            <item index="26" class="java.lang.String" itemvalue="huggingface-hub" />
            <item index="27" class="java.lang.String" itemvalue="PyYAML" />
            <item index="28" class="java.lang.String" itemvalue="py-cpuinfo" />
            <item index="29" class="java.lang.String" itemvalue="psutil" />
            <item index="30" class="java.lang.String" itemvalue="albumentations" />
            <item index="31" class="java.lang.String" itemvalue="onnxruntime" />
            <item index="32" class="java.lang.String" itemvalue="onnxslim" />
            <item index="33" class="java.lang.String" itemvalue="safetensors" />
            <item index="34" class="java.lang.String" itemvalue="onnx" />
            <item index="35" class="java.lang.String" itemvalue="pycocotools" />
            <item index="36" class="java.lang.String" itemvalue="torch" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>