{"time": "2025-08-04 00:32:42.729876", "level": "INFO", "message": "aisrv kaiwu_rl_helper start at pid 2928, ppid is 132974908553024, thread id is -1", "file": "kaiwu_rl_helper_standard.py", "line": "134", "module": "aisrv", "process": "kaiwu_rl_helper_standard", "function": "__init__", "stack": "", "pid": 2928}
{"time": "2025-08-04 00:32:42.732956", "level": "INFO", "message": "aisrv aisrvhandle use kaiwu_rl_helper: <KaiWuRLStandardHelper(kaiwu_rl_helper_0, initial daemon)>", "file": "aisrv_server_standard.py", "line": "794", "module": "aisrv", "process": "aisrv_server_standard", "function": "before_run", "stack": "", "pid": 2928}
{"time": "2025-08-04 00:32:42.734974", "level": "INFO", "message": "aisrv aisrvhandle established connect to 172.20.0.4:5566, slot id is 0, min_slot_id is 0", "file": "aisrv_server_standard.py", "line": "797", "module": "aisrv", "process": "aisrv_server_standard", "function": "before_run", "stack": "", "pid": 2928}
{"time": "2025-08-04 00:32:42.737406", "level": "INFO", "message": "aisrv aisrvhandle current_actor_addrs is ['127.0.0.1:8888'], current_learner_addrs is ['127.0.0.1:9999']", "file": "aisrv_server_standard.py", "line": "806", "module": "aisrv", "process": "aisrv_server_standard", "function": "before_run", "stack": "", "pid": 2928}
{"time": "2025-08-04 00:32:42.740311", "level": "INFO", "message": "aisrv aisrvhandle start success at pid 2928", "file": "aisrv_server_standard.py", "line": "837", "module": "aisrv", "process": "aisrv_server_standard", "function": "before_run", "stack": "", "pid": 2928}
{"time": "2025-08-04 00:32:43.351858", "level": "INFO", "message": "aisrv env proxy, zmq_client connect to 172.20.0.4:5566 success, client_id 17986667", "file": "env_proxy_lazy.py", "line": "53", "module": "aisrv", "process": "env_proxy_lazy", "function": "__init__", "stack": "", "pid": 2928}
{"time": "2025-08-04 00:32:43.354112", "level": "INFO", "message": "aisrv kaiwu_rl_helper start agent 0 with train_one", "file": "kaiwu_rl_helper_standard.py", "line": "1018", "module": "aisrv", "process": "kaiwu_rl_helper_standard", "function": "start_agent", "stack": "", "pid": 2928}
{"time": "2025-08-04 00:32:43.420245", "level": "INFO", "message": "aisrv training_metrics is {'basic': {'train_global_step': 0, 'sample_production_and_consumption_ratio': 0, 'episode_cnt': 0, 'sample_receive_cnt': 0, 'predict_succ_cnt': 0.0, 'load_model_succ_cnt': 0.0}, 'algorithm': {'reward': -0.01, 'q_value': 0.19, 'value_loss': 0.01, 'policy_loss': 0, 'entropy_loss': 0}, 'env': {'total_score': 0, 'treasure_score': 0, 'max_steps': 0, 'finished_steps': 0, 'treasure_random': 0, 'total_treasures': 0, 'collected_treasures': 0, 'buff_cnt': 0, 'buff_cooldown': 0, 'talent_cooldown': 0, 'talent_cnt': 0, 'buff_random': 0, 'obstacle_count': 0, 'obstacle_random': 0}, 'diy': {'diy_1': 0.0, 'diy_2': 0.0, 'diy_3': 0.0, 'diy_4': 0.0, 'diy_5': 0.0}}", "file": "train_workflow.py", "line": "70", "module": "aisrv", "process": "train_workflow", "function": "run_episodes", "stack": "", "pid": 2928}
{"time": "2025-08-04 00:32:43.600844", "level": "INFO", "message": "aisrv EnvProxy reset one game success", "file": "env_proxy_lazy.py", "line": "100", "module": "aisrv", "process": "env_proxy_lazy", "function": "reset", "stack": "", "pid": 2928}
{"time": "2025-08-04 00:32:46.102477", "level": "INFO", "message": "aisrv training_metrics is {'basic': {'train_global_step': 0, 'sample_production_and_consumption_ratio': 0, 'episode_cnt': 0, 'sample_receive_cnt': 0, 'predict_succ_cnt': 0.0, 'load_model_succ_cnt': 0.0}, 'algorithm': {'reward': -0.01, 'q_value': 0.19, 'value_loss': 0.01, 'policy_loss': 0, 'entropy_loss': 0}, 'env': {'total_score': 0, 'treasure_score': 0, 'max_steps': 0, 'finished_steps': 0, 'treasure_random': 0, 'total_treasures': 0, 'collected_treasures': 0, 'buff_cnt': 0, 'buff_cooldown': 0, 'talent_cooldown': 0, 'talent_cnt': 0, 'buff_random': 0, 'obstacle_count': 0, 'obstacle_random': 0}, 'diy': {'diy_1': 0.0, 'diy_2': 0.0, 'diy_3': 0.0, 'diy_4': 0.0, 'diy_5': 0.0}}", "file": "train_workflow.py", "line": "70", "module": "aisrv", "process": "train_workflow", "function": "run_episodes", "stack": "", "pid": 2928}
