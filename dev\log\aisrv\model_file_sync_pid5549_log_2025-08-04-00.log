{"time": "2025-08-04 00:40:32.247997", "level": "INFO", "message": "aisrv model_file_sync, use modelpool, use_which_deep_learning_framework is pytorch", "file": "model_file_sync.py", "line": "189", "module": "aisrv", "process": "model_file_sync", "function": "before_run", "stack": "", "pid": 5549}
{"time": "2025-08-04 00:40:32.250893", "level": "INFO", "message": "model_file_sync process pid is 5549", "file": "model_file_sync.py", "line": "195", "module": "aisrv", "process": "model_file_sync", "function": "before_run", "stack": "", "pid": 5549}
{"time": "2025-08-04 00:40:32.252057", "level": "INFO", "message": "model_file_sync mkdir /data/ckpt/back_to_the_realm_v2_ppo/plugins success", "file": "model_file_sync.py", "line": "138", "module": "aisrv", "process": "model_file_sync", "function": "make_model_dirs", "stack": "", "pid": 5549}
{"time": "2025-08-04 00:40:32.252933", "level": "INFO", "message": "model_file_sync mkdir /data/ckpt/back_to_the_realm_v2_ppo/models /data/ckpt/back_to_the_realm_v2_ppo/plugins success", "file": "model_file_sync.py", "line": "144", "module": "aisrv", "process": "model_file_sync", "function": "make_model_dirs", "stack": "", "pid": 5549}
{"time": "2025-08-04 00:40:32.253727", "level": "INFO", "message": "model_file_sync mkdir /data/ckpt/back_to_the_realm_v2_ppo/convert_models_aisrv success", "file": "model_file_sync.py", "line": "153", "module": "aisrv", "process": "model_file_sync", "function": "make_model_dirs", "stack": "", "pid": 5549}
