# Environment Data Log

## Step 70 - Environment Data

```json
{
  "obs": {
    "frame_state": {
      "step_no": 70,
      "heroes": [
        {
          "hero_id": 1112,
          "pos": {
            "x": 57,
            "z": 106
          },
          "talent": {
            "talent_type": 1,
            "status": 1,
            "cooldown": 0
          },
          "speed_up": 0,
          "buff_remain_time": 0
        }
      ],
      "organs": [
        {
          "sub_type": 2,
          "status": 1,
          "pos": {
            "x": 57,
            "z": 68
          },
          "relative_pos": {
            "direction": "South",
            "l2_distance": "Small"
          },
          "config_id": 0,
          "cooldown": 0
        },
        {
          "sub_type": 3,
          "config_id": 21,
          "status": 1,
          "pos": {
            "x": 55,
            "z": 105
          },
          "relative_pos": {
            "direction": "SouthWest",
            "l2_distance": "VerySmall"
          },
          "cooldown": 0
        },
        {
          "sub_type": 4,
          "config_id": 22,
          "status": -1,
          "pos": {
            "x": -1,
            "z": -1
          },
          "relative_pos": {
            "direction": "SouthEast",
            "l2_distance": "Large"
          },
          "cooldown": 0
        }
      ]
    },
    "score_info": {
      "total_score": 185.0,
      "step_no": 70,
      "score": 0.0,
      "treasure_collected_count": 0,
      "treasure_score": 0,
      "buff_count": 0,
      "talent_count": 0
    },
    "map_info": [
      {
        "values": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1]
      },
      {
        "values": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1]
      },
      {
        "values": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0]
      },
      {
        "values": [1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0]
      },
      {
        "values": [1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0]
      },
      {
        "values": [1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0]
      },
      {
        "values": [1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0]
      },
      {
        "values": [1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0]
      },
      {
        "values": [1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0]
      },
      {
        "values": [1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0]
      },
      {
        "values": [1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0]
      }
    ],
    "legal_act": [1, 1]
  },
  "extra_info": {
    "result_message": "ok",
    "frame_state": {
      "step_no": 70,
      "heroes": [
        {
          "hero_id": 1112,
          "pos": {
            "x": 57,
            "z": 106
          },
          "talent": {
            "talent_type": 1,
            "status": 1,
            "cooldown": 0
          },
          "speed_up": 0,
          "buff_remain_time": 0
        }
      ],
      "organs": [
        {
          "sub_type": 2,
          "status": 1,
          "pos": {
            "x": 57,
            "z": 68
          },
          "relative_pos": {
            "direction": "South",
            "l2_distance": "Small"
          },
          "config_id": 0,
          "cooldown": 0
        },
        {
          "sub_type": 3,
          "config_id": 21,
          "status": 1,
          "pos": {
            "x": 55,
            "z": 105
          },
          "relative_pos": {
            "direction": "SouthWest",
            "l2_distance": "VerySmall"
          },
          "cooldown": 0
        },
        {
          "sub_type": 4,
          "config_id": 22,
          "status": 1,
          "pos": {
            "x": 95,
            "z": 50
          },
          "relative_pos": {
            "direction": "SouthEast",
            "l2_distance": "Large"
          },
          "cooldown": 0
        }
      ]
    },
    "game_info": {
      "total_score": 185.8,
      "step_no": 70,
      "pos": {
        "x": 57,
        "z": 106
      },
      "start_pos": {
        "x": 55,
        "z": 105
      },
      "end_pos": {
        "x": 95,
        "z": 50
      },
      "treasure_count": 2,
      "buff_duration": 51,
      "map_info": [
        {
          "values": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1]
        },
        {
          "values": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1]
        },
        {
          "values": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0]
        },
        {
          "values": [1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0]
        },
        {
          "values": [1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0]
        },
        {
          "values": [1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0]
        },
        {
          "values": [1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0]
        },
        {
          "values": [1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0]
        },
        {
          "values": [1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0]
        },
        {
          "values": [1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0]
        },
        {
          "values": [1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0]
        }
      ],
      "score": 0.0,
      "treasure_collected_count": 0,
      "treasure_score": 0,
      "buff_count": 0,
      "talent_count": 0,
      "buff_remain_time": 0,
      "obstacle_id": []
    },
    "result_code": 0
  }
}
```

---

## 数据分析

### 游戏状态概览
- **步数**: 70
- **当前位置**: (57, 106)
- **起始位置**: (55, 105)
- **终点位置**: (95, 50)
- **总分**: 185.8

### 器官状态
1. **sub_type 2** (状态: 1) - 位置: (57, 68) - 方向: South, 距离: Small
2. **sub_type 3** (状态: 1) - 位置: (55, 105) - 方向: SouthWest, 距离: VerySmall
3. **sub_type 4** (状态: 1) - 位置: (95, 50) - 方向: SouthEast, 距离: Large

### 地图信息
11x11的地图，其中1表示可通行区域，0表示障碍物或不可通行区域。
