{"time": "2025-08-04 00:45:55.129466", "level": "INFO", "message": "aisrv aisrvhandle use kaiwu_rl_helper: <KaiWuRL<PERSON><PERSON>dardHelper(kaiwu_rl_helper_0, initial daemon)>", "file": "aisrv_server_standard.py", "line": "794", "module": "aisrv", "process": "aisrv_server_standard", "function": "before_run", "stack": "", "pid": 8782}
{"time": "2025-08-04 00:45:55.131084", "level": "INFO", "message": "aisrv aisrvhandle established connect to 172.20.0.4:5566, slot id is 0, min_slot_id is 0", "file": "aisrv_server_standard.py", "line": "797", "module": "aisrv", "process": "aisrv_server_standard", "function": "before_run", "stack": "", "pid": 8782}
{"time": "2025-08-04 00:45:55.132327", "level": "INFO", "message": "aisrv aisrvhandle current_actor_addrs is ['127.0.0.1:8888'], current_learner_addrs is ['127.0.0.1:9999']", "file": "aisrv_server_standard.py", "line": "806", "module": "aisrv", "process": "aisrv_server_standard", "function": "before_run", "stack": "", "pid": 8782}
{"time": "2025-08-04 00:45:55.133465", "level": "INFO", "message": "aisrv aisrvhandle start success at pid 8782", "file": "aisrv_server_standard.py", "line": "837", "module": "aisrv", "process": "aisrv_server_standard", "function": "before_run", "stack": "", "pid": 8782}
