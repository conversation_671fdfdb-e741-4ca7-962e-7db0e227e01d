{"time": "2025-08-04 00:32:32.567699", "level": "INFO", "message": "learner train process start at pid is 2600", "file": "trainer.py", "line": "32", "module": "learner", "process": "trainer", "function": "__init__", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:32.624022", "level": "INFO", "message": "learner train replaybuff, use reverb", "file": "replay_buffer_wrapper.py", "line": "58", "module": "learner", "process": "replay_buffer_wrapper", "function": "__init__", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:32.640813", "level": "INFO", "message": "learner model_file_save process start, type is 0, no need get mode file from cos", "file": "model_file_save.py", "line": "776", "module": "learner", "process": "model_file_save", "function": "start_actor_process_by_type", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:33.009907", "level": "INFO", "message": "learner monitor_proxy process start success at pid is 2717", "file": "monitor_proxy_process.py", "line": "73", "module": "learner", "process": "monitor_proxy_process", "function": "before_run", "stack": "", "pid": 2717}
{"time": "2025-08-04 00:32:33.587700", "level": "INFO", "message": "learner policy_name train_one, algo ppo, model_wrapper is StandardModelWrapperPytorch", "file": "model_wrapper_common.py", "line": "99", "module": "learner", "process": "model_wrapper_common", "function": "create_standard_model_wrapper", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:33.616202", "level": "INFO", "message": "learner train zmq server on learner bind at 0.0.0.0:9997 for aisrv", "file": "on_policy_trainer.py", "line": "691", "module": "learner", "process": "on_policy_trainer", "function": "before_run", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:33.630383", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-0.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:33.676745", "level": "INFO", "message": "model_file_sync push output_file_name /data/ckpt/back_to_the_realm_v2_ppo/kaiwu_checkpoint_back_to_the_realm_v2_ppo_0.tar.gz key model.ckpt_back_to_the_realm_v2_ppo_0 to modelpool success,             total push to modelpool succ cnt is 1             total push to modelpool err cnt is 0", "file": "model_file_sync.py", "line": "560", "module": "learner", "process": "model_file_sync", "function": "push_checkpoint_to_model_pool", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:33.678224", "level": "INFO", "message": "learner train first model file push to modelpool success", "file": "on_policy_trainer.py", "line": "723", "module": "learner", "process": "on_policy_trainer", "function": "before_run", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:33.682620", "level": "INFO", "message": "learner train process start success at 2600, on-policy/off-policy is off-policy, ppo trainer global step -1.0, load app back_to_the_realm_v2 algo ppo model, train_batch_size is 2", "file": "on_policy_trainer.py", "line": "756", "module": "learner", "process": "on_policy_trainer", "function": "before_run", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:33.741153", "level": "INFO", "message": "learner start_background_filler success, reverb.Client connect at localhost:9999", "file": "reverb_dataset_v1.py", "line": "57", "module": "learner", "process": "reverb_dataset_v1", "function": "start_background_filler", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:33.973205", "level": "INFO", "message": "learner model_file_sync, use modelpool, use_which_deep_learning_framework is pytorch", "file": "model_file_sync.py", "line": "189", "module": "learner", "process": "model_file_sync", "function": "before_run", "stack": "", "pid": 2771}
{"time": "2025-08-04 00:32:33.976356", "level": "INFO", "message": "model_file_sync process pid is 2771", "file": "model_file_sync.py", "line": "195", "module": "learner", "process": "model_file_sync", "function": "before_run", "stack": "", "pid": 2771}
{"time": "2025-08-04 00:32:33.977820", "level": "INFO", "message": "model_file_sync mkdir /data/ckpt/back_to_the_realm_v2_ppo/convert_models_learner success", "file": "model_file_sync.py", "line": "153", "module": "learner", "process": "model_file_sync", "function": "make_model_dirs", "stack": "", "pid": 2771}
{"time": "2025-08-04 00:32:33.979906", "level": "INFO", "message": "learner ppid is 132974908553024", "file": "monitor_proxy.py", "line": "36", "module": "learner", "process": "monitor_proxy", "function": "__init__", "stack": "", "pid": 2771}
{"time": "2025-08-04 00:32:34.351719", "level": "INFO", "message": "learner ppid is 132974908553024", "file": "monitor_proxy.py", "line": "36", "module": "learner", "process": "monitor_proxy", "function": "__init__", "stack": "", "pid": 2776}
{"time": "2025-08-04 00:32:34.355710", "level": "INFO", "message": "model_file_save process start success at pid 2776", "file": "model_file_save.py", "line": "300", "module": "learner", "process": "model_file_save", "function": "before_run", "stack": "", "pid": 2776}
{"time": "2025-08-04 00:32:46.176846", "level": "INFO", "message": "learner train_step is 0, so not save_model", "file": "standard_model_wrapper_pytorch.py", "line": "238", "module": "learner", "process": "standard_model_wrapper_pytorch", "function": "save_param_detail", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:46.178516", "level": "INFO", "message": "learner train learner save_param_by_framework is success, ip is 172.20.0.5_0", "file": "on_policy_trainer.py", "line": "974", "module": "learner", "process": "on_policy_trainer", "function": "save_model_detail", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:46.180382", "level": "INFO", "message": "learner train learner really save_model from aisrv 172.20.0.5_0", "file": "on_policy_trainer.py", "line": "1055", "module": "learner", "process": "on_policy_trainer", "function": "learner_process_message_by_aisrv", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:46.671097", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-1.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:46.689221", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-2.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:46.721875", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-3.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:46.743597", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-4.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:46.769770", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-5.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:46.798495", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-6.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:46.820714", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-7.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:46.850274", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-8.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:46.874734", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-9.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:46.888936", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-10.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:46.915381", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-11.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:46.949551", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-12.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:46.968972", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-13.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:46.989059", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-14.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:47.008477", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-15.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:47.025085", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-16.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:47.044307", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-17.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:47.066917", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-18.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:47.084931", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-19.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:47.102919", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-20.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:47.125887", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-21.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:47.146685", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-22.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:47.164823", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-23.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:47.181126", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-24.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:47.195885", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-25.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:47.216343", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-26.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:47.233117", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-27.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:47.248974", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-28.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:47.268001", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-29.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:47.285733", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-30.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:47.305123", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-31.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:47.320448", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-32.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:47.344821", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-33.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:47.362877", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-34.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:47.381464", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-35.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:47.397361", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-36.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:47.422776", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-37.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:47.440961", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-38.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:47.463289", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-39.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:47.478528", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-40.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:47.494134", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-41.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:47.512846", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-42.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:47.528336", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-43.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:47.548044", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-44.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:47.569138", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-45.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:47.594685", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-46.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
{"time": "2025-08-04 00:32:47.613092", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-47.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 2600}
