# Organs Data Log

## 器官数据分析

### Step 1 - 器官状态变化

```json
{
  "obs_organs": [
    {
      "sub_type": 2,
      "status": 1,
      "pos": {"x": 57, "z": 68},
      "relative_pos": {"direction": "NorthWest", "l2_distance": "Small"},
      "config_id": 0,
      "cooldown": 0
    },
    {
      "sub_type": 3,
      "config_id": 21,
      "status": 1,
      "pos": {"x": 84, "z": 53},
      "relative_pos": {"direction": "East", "l2_distance": "VerySmall"},
      "cooldown": 0
    },
    {
      "sub_type": 4,
      "config_id": 22,
      "status": -1,
      "pos": {"x": -1, "z": -1},
      "relative_pos": {"direction": "NorthWest", "l2_distance": "Large"},
      "cooldown": 0
    }
  ],
  "extra_info_organs": [
    {
      "sub_type": 2,
      "status": 1,
      "pos": {"x": 57, "z": 68},
      "relative_pos": {"direction": "NorthWest", "l2_distance": "Small"},
      "config_id": 0,
      "cooldown": 0
    },
    {
      "sub_type": 3,
      "config_id": 21,
      "status": 1,
      "pos": {"x": 84, "z": 53},
      "relative_pos": {"direction": "East", "l2_distance": "VerySmall"},
      "cooldown": 0
    },
    {
      "sub_type": 4,
      "config_id": 22,
      "status": 1,
      "pos": {"x": 48, "z": 114},
      "relative_pos": {"direction": "NorthWest", "l2_distance": "Large"},
      "cooldown": 0
    }
  ]
}
```

**关键差异**: sub_type 4 在 obs 中状态为 -1 (不可见)，位置为 (-1, -1)，但在 extra_info 中状态为 1，位置为 (48, 114)

---

### Step 2 - 器官状态变化

```json
{
  "obs_organs": [
    {
      "sub_type": 2,
      "status": 1,
      "pos": {"x": 57, "z": 68},
      "relative_pos": {"direction": "NorthWest", "l2_distance": "Small"},
      "config_id": 0,
      "cooldown": 0
    },
    {
      "sub_type": 3,
      "config_id": 21,
      "status": 1,
      "pos": {"x": 84, "z": 53},
      "relative_pos": {"direction": "SouthEast", "l2_distance": "VerySmall"},
      "cooldown": 0
    },
    {
      "sub_type": 4,
      "config_id": 22,
      "status": -1,
      "pos": {"x": -1, "z": -1},
      "relative_pos": {"direction": "NorthWest", "l2_distance": "Large"},
      "cooldown": 0
    }
  ],
  "extra_info_organs": [
    {
      "sub_type": 2,
      "status": 1,
      "pos": {"x": 57, "z": 68},
      "relative_pos": {"direction": "NorthWest", "l2_distance": "Small"},
      "config_id": 0,
      "cooldown": 0
    },
    {
      "sub_type": 3,
      "config_id": 21,
      "status": 1,
      "pos": {"x": 84, "z": 53},
      "relative_pos": {"direction": "SouthEast", "l2_distance": "VerySmall"},
      "cooldown": 0
    },
    {
      "sub_type": 4,
      "config_id": 22,
      "status": 1,
      "pos": {"x": 48, "z": 114},
      "relative_pos": {"direction": "NorthWest", "l2_distance": "Large"},
      "cooldown": 0
    }
  ]
}
```

**变化**: sub_type 3 的相对方向从 "East" 变为 "SouthEast"

---

### Step 3 - 器官状态变化

```json
{
  "obs_organs": [
    {
      "sub_type": 2,
      "status": 1,
      "pos": {"x": 57, "z": 68},
      "relative_pos": {"direction": "NorthWest", "l2_distance": "Small"},
      "config_id": 0,
      "cooldown": 0
    },
    {
      "sub_type": 3,
      "config_id": 21,
      "status": 1,
      "pos": {"x": 84, "z": 53},
      "relative_pos": {"direction": "SouthEast", "l2_distance": "VerySmall"},
      "cooldown": 0
    },
    {
      "sub_type": 4,
      "config_id": 22,
      "status": -1,
      "pos": {"x": -1, "z": -1},
      "relative_pos": {"direction": "NorthWest", "l2_distance": "Large"},
      "cooldown": 0
    }
  ],
  "extra_info_organs": [
    {
      "sub_type": 2,
      "status": 1,
      "pos": {"x": 57, "z": 68},
      "relative_pos": {"direction": "NorthWest", "l2_distance": "Small"},
      "config_id": 0,
      "cooldown": 0
    },
    {
      "sub_type": 3,
      "config_id": 21,
      "status": 1,
      "pos": {"x": 84, "z": 53},
      "relative_pos": {"direction": "SouthEast", "l2_distance": "VerySmall"},
      "cooldown": 0
    },
    {
      "sub_type": 4,
      "config_id": 22,
      "status": 1,
      "pos": {"x": 48, "z": 114},
      "relative_pos": {"direction": "NorthWest", "l2_distance": "Large"},
      "cooldown": 0
    }
  ]
}
```

**状态**: 与上一步相同，无变化

---

## 数据分析总结

### 器官类型说明
- **sub_type 2**: 位置固定在 (57, 68)，状态始终为 1
- **sub_type 3**: 位置固定在 (84, 53)，但相对方向会变化 (East ↔ SouthEast)
- **sub_type 4**: 在 obs 中经常不可见 (status: -1, pos: (-1, -1))，但在 extra_info 中可见 (status: 1, pos: (48, 114))

### 关键发现
1. **数据不一致性**: obs 和 extra_info 中的 sub_type 4 器官数据不一致
2. **相对方向变化**: sub_type 3 的相对方向在 "East" 和 "SouthEast" 之间变化，反映了角色的移动
3. **终点器官**: sub_type 4 可能是终点器官，在某些情况下对 obs 不可见但在 extra_info 中可见

### Step 4-12 - 重复模式观察

从后续的多个步骤中观察到相同的模式重复出现：

```json
{
  "pattern_observation": {
    "sub_type_2": {
      "position": {"x": 57, "z": 68},
      "status": "始终为 1",
      "relative_direction": "始终为 NorthWest",
      "relative_distance": "始终为 Small"
    },
    "sub_type_3": {
      "position": {"x": 84, "z": 53},
      "status": "始终为 1",
      "relative_direction": "在 East 和 SouthEast 之间变化",
      "relative_distance": "始终为 VerySmall"
    },
    "sub_type_4": {
      "obs_status": "经常为 -1 (不可见)",
      "obs_position": "(-1, -1) 当不可见时",
      "extra_info_status": "始终为 1",
      "extra_info_position": "(48, 114)",
      "relative_direction": "始终为 NorthWest",
      "relative_distance": "始终为 Large"
    }
  }
}
```

### 数据一致性分析

| 步骤 | sub_type 3 方向 (obs) | sub_type 3 方向 (extra_info) | sub_type 4 状态 (obs) | sub_type 4 状态 (extra_info) |
|------|---------------------|----------------------------|---------------------|----------------------------|
| 1    | East                | East                       | -1                  | 1                          |
| 2    | SouthEast           | SouthEast                  | -1                  | 1                          |
| 3    | SouthEast           | SouthEast                  | -1                  | 1                          |
| 4-12 | East/SouthEast 交替  | East/SouthEast 交替         | -1                  | 1                          |

### 建议
- 使用 extra_info 中的器官数据作为真实状态
- 监控 sub_type 4 的状态变化，这可能影响终点位置的预测
- sub_type 3 的方向变化可能反映了角色的移动轨迹
