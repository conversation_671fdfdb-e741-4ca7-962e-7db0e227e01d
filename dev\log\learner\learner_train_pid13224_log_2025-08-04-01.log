{"time": "2025-08-04 01:05:04.074810", "level": "INFO", "message": "learner train process start at pid is 13224", "file": "trainer.py", "line": "32", "module": "learner", "process": "trainer", "function": "__init__", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:04.121930", "level": "INFO", "message": "learner train replaybuff, use reverb", "file": "replay_buffer_wrapper.py", "line": "58", "module": "learner", "process": "replay_buffer_wrapper", "function": "__init__", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:04.151882", "level": "INFO", "message": "learner model_file_save process start, type is 0, no need get mode file from cos", "file": "model_file_save.py", "line": "776", "module": "learner", "process": "model_file_save", "function": "start_actor_process_by_type", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:04.541054", "level": "INFO", "message": "learner monitor_proxy process start success at pid is 13384", "file": "monitor_proxy_process.py", "line": "73", "module": "learner", "process": "monitor_proxy_process", "function": "before_run", "stack": "", "pid": 13384}
{"time": "2025-08-04 01:05:05.668666", "level": "INFO", "message": "learner policy_name train_one, algo ppo, model_wrapper is StandardModelWrapperPytorch", "file": "model_wrapper_common.py", "line": "99", "module": "learner", "process": "model_wrapper_common", "function": "create_standard_model_wrapper", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:05.714385", "level": "INFO", "message": "learner train zmq server on learner bind at 0.0.0.0:9997 for aisrv", "file": "on_policy_trainer.py", "line": "691", "module": "learner", "process": "on_policy_trainer", "function": "before_run", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:05.732249", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-0.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:05.820887", "level": "INFO", "message": "model_file_sync push output_file_name /data/ckpt/back_to_the_realm_v2_ppo/kaiwu_checkpoint_back_to_the_realm_v2_ppo_0.tar.gz key model.ckpt_back_to_the_realm_v2_ppo_0 to modelpool success,             total push to modelpool succ cnt is 1             total push to modelpool err cnt is 0", "file": "model_file_sync.py", "line": "560", "module": "learner", "process": "model_file_sync", "function": "push_checkpoint_to_model_pool", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:05.821937", "level": "INFO", "message": "learner train first model file push to modelpool success", "file": "on_policy_trainer.py", "line": "723", "module": "learner", "process": "on_policy_trainer", "function": "before_run", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:05.828638", "level": "INFO", "message": "learner train process start success at 13224, on-policy/off-policy is off-policy, ppo trainer global step -1.0, load app back_to_the_realm_v2 algo ppo model, train_batch_size is 2", "file": "on_policy_trainer.py", "line": "756", "module": "learner", "process": "on_policy_trainer", "function": "before_run", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:05.849177", "level": "INFO", "message": "learner start_background_filler success, reverb.Client connect at localhost:9999", "file": "reverb_dataset_v1.py", "line": "57", "module": "learner", "process": "reverb_dataset_v1", "function": "start_background_filler", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:06.089148", "level": "INFO", "message": "learner model_file_sync, use modelpool, use_which_deep_learning_framework is pytorch", "file": "model_file_sync.py", "line": "189", "module": "learner", "process": "model_file_sync", "function": "before_run", "stack": "", "pid": 13472}
{"time": "2025-08-04 01:05:06.092043", "level": "INFO", "message": "model_file_sync process pid is 13472", "file": "model_file_sync.py", "line": "195", "module": "learner", "process": "model_file_sync", "function": "before_run", "stack": "", "pid": 13472}
{"time": "2025-08-04 01:05:06.093680", "level": "INFO", "message": "model_file_sync mkdir /data/ckpt/back_to_the_realm_v2_ppo/convert_models_learner success", "file": "model_file_sync.py", "line": "153", "module": "learner", "process": "model_file_sync", "function": "make_model_dirs", "stack": "", "pid": 13472}
{"time": "2025-08-04 01:05:06.096545", "level": "INFO", "message": "learner ppid is 129657108588352", "file": "monitor_proxy.py", "line": "36", "module": "learner", "process": "monitor_proxy", "function": "__init__", "stack": "", "pid": 13472}
{"time": "2025-08-04 01:05:06.519004", "level": "INFO", "message": "learner ppid is 129657108588352", "file": "monitor_proxy.py", "line": "36", "module": "learner", "process": "monitor_proxy", "function": "__init__", "stack": "", "pid": 13477}
{"time": "2025-08-04 01:05:06.524942", "level": "INFO", "message": "model_file_save process start success at pid 13477", "file": "model_file_save.py", "line": "300", "module": "learner", "process": "model_file_save", "function": "before_run", "stack": "", "pid": 13477}
{"time": "2025-08-04 01:05:16.294063", "level": "INFO", "message": "learner train_step is 0, so not save_model", "file": "standard_model_wrapper_pytorch.py", "line": "238", "module": "learner", "process": "standard_model_wrapper_pytorch", "function": "save_param_detail", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:16.295524", "level": "INFO", "message": "learner train learner save_param_by_framework is success, ip is 172.20.0.5_0", "file": "on_policy_trainer.py", "line": "974", "module": "learner", "process": "on_policy_trainer", "function": "save_model_detail", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:16.296554", "level": "INFO", "message": "learner train learner really save_model from aisrv 172.20.0.5_0", "file": "on_policy_trainer.py", "line": "1055", "module": "learner", "process": "on_policy_trainer", "function": "learner_process_message_by_aisrv", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:17.517430", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-1.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:17.547803", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-2.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:17.572654", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-3.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:17.597536", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-4.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:17.617432", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-5.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:17.642580", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-6.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:17.669027", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-7.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:17.691347", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-8.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:17.716034", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-9.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:17.736428", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-10.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:17.764399", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-11.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:17.788078", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-12.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:17.815496", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-13.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:17.843391", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-14.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:17.869192", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-15.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:17.896553", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-16.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:17.928612", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-17.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:17.954513", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-18.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:17.980727", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-19.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:18.007121", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-20.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:18.037268", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-21.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:18.064434", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-22.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:18.092280", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-23.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:18.121496", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-24.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:18.156997", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-25.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:18.188891", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-26.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:18.219578", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-27.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:18.254858", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-28.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:18.294314", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-29.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:18.325989", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-30.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:18.357097", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-31.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:18.382065", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-32.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:18.418516", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-33.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:18.459210", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-34.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:18.502384", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-35.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:18.552198", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-36.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:18.586766", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-37.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:18.630024", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-38.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:18.676632", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-39.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:18.703509", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-40.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:18.745887", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-41.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:18.776047", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-42.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:18.800324", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-43.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:18.828601", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-44.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:18.860205", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-45.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:18.892529", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-46.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
{"time": "2025-08-04 01:05:18.932367", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-47.pkl successfully", "file": "agent.py", "line": "129", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 13224}
