2025-08-04 06:05:33.290 | INFO | PID:282 | model_file_sync.before_run:189 - aisrv model_file_sync, use modelpool, use_which_deep_learning_framework is pytorch
2025-08-04 06:05:33.293 | INFO | PID:282 | model_file_sync.before_run:195 - model_file_sync process pid is 282
2025-08-04 06:05:33.296 | INFO | PID:279 | model_file_sync.make_model_dirs:138 - model_file_sync mkdir /data/ckpt/back_to_the_realm_v2_ppo/plugins success
2025-08-04 06:05:33.299 | INFO | PID:282 | model_file_sync.make_model_dirs:144 - model_file_sync mkdir /data/ckpt/back_to_the_realm_v2_ppo/models /data/ckpt/back_to_the_realm_v2_ppo/plugins success
2025-08-04 06:05:33.302 | INFO | PID:282 | model_file_sync.make_model_dirs:153 - model_file_sync mkdir /data/ckpt/back_to_the_realm_v2_ppo/convert_models_aisrv success
2025-08-04 06:05:33.306 | INFO | PID:282 | monitor_proxy.__init__:36 - aisrv ppid is 136589543036736
2025-08-04 06:05:33.403 | INFO | PID:262 | aisrv_server_standard.start_aisrv_handler:484 - aisrv AiServer AiSrvHandle with address: 172.12025-08-04 06:05:32025-08-04 06:05:33.420 | INFO | PID:284 | monitor_proxy_process.before_run:73 - aisrv monitor_proxy process start succ2025-08-04 06:05:33.411 | INFO | PID:264 | aisrv_server_standard.start_aisrv_handler:484 - aisrv AiServer AiSrvHandle with address: ***********:5566 start
2025-08-04 06:05:33.533 | INFO | PID:264 | aisrv_server_standard.start_aisrv_handler:484 - aisrv AiServer AiSrvHandle with address: ***********:5566 start
2025-08-04 06:05:33.547 | INFO | PID:288 | kaiwu_rl_helper_standard.__init__:134 - aisrv kaiwu_rl_helper start at pid 288, ppid is 136589543036736, thread id is -1
2025-08-04 06:05:33.569 | INFO | PID:288 | aisrv_server_standard.before_run:794 - aisrv aisrvhandle use kaiwu_rl_helper: <KaiWuRLStandardHelper(kaiwu_rl_helper_0, initial daemon)>
2025-08-04 06:05:33.575 | INFO | PID:288 | aisrv_server_standard.before_run:797 - aisrv aisrvhandle established connect to ***********:5566, slot id is 0, min_slot_id is 0
2025-08-04 06:05:33.581 | INFO | PID:288 | aisrv_server_standard.before_run:806 - aisrv aisrvhandle current_actor_addrs is ['127.0.0.1:8888'], current_learner_addrs is ['***********:9999']
2025-08-04 06:05:33.593 | INFO | PID:288 | aisrv_server_standard.before_run:837 - aisrv aisrvhandle start success at pid 288
2025-08-04 06:05:33.594 | INFO | PID:264 | aisrv_server_standard.start_aisrv_handler:484 - aisrv AiServer AiSrvHandle with address: ***********:5566 start
2025-08-04 06:05:33.706 | INFO | PID:264 | aisrv_server_standard.start_aisrv_handler:484 - aisrv AiServer AiSrvHandle with address: ***********:5566 start
2025-08-04 06:05:33.792 | INFO | PID:264 | aisrv_server_standard.before_run:604 - aisrv AiServer is start success at 0.0.0.0:8000, pid is 264, run_mode is train, self_play is False
2025-08-04 06:05:34.526443: I tensorflow/core/util/port.cc:110] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
2025-08-04 06:05:36.233078: I tensorflow/core/platform/cpu_feature_guard.cc:182] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.
To enable the following instructions: AVX2 AVX_VNNI FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.
2025-08-04 06:05:43.758 | INFO | PID:288 | env_proxy_lazy.__init__:53 - aisrv env proxy, zmq_client connect to ***********:5566 success, client_id 96558291
2025-08-04 06:05:43.762 | INFO | PID:288 | kaiwu_rl_helper_standard.start_agent:1018 - aisrv kaiwu_rl_helper start agent 0 with train_one
2025-08-04 06:05:43.820 | INFO | PID:288 | train_workflow.run_episodes:104 - aisrv training_metrics is {'basic': {'train_global_step': 0, 'sample_production_and_consumption_ratio': 0, 'episode_cnt': 0, 'sample_receive_cnt': 0, 'predict_succ_cnt': 0, 'load_model_succ_cnt': 0}, 'algorithm': {'reward': 0, 'q_value': 0, 'value_loss': 0, 'policy_loss': 0, 'entropy_loss': 0}, 'env': {'total_score': 0, 'treasure_score': 0, 'max_steps': 0, 'finished_steps': 0, 'treasure_random': 0, 'total_treasures': 0, 'collected_treasures': 0, 'buff_cnt': 0, 'buff_cooldown': 0, 'talent_cooldown': 0, 'talent_cnt': 0, 'buff_random': 0, 'obstacle_count': 0, 'obstacle_random': 0}, 'diy': {'diy_1': 0, 'diy_2': 0, 'diy_3': 0, 'diy_4': 0, 'diy_5': 0}}
2025-08-04 06:05:43.863 | INFO | PID:283 | model_wrapper_common.create_standard_model_wrapper:99 - aisrv policy_name train_one, algo ppo, model_wrapper is StandardModelWrapperPytorch
2025-08-04 06:05:43.876 | INFO | PID:283 | multi_model_common.init_load_models:240 - aisrv predict just no model_pool files or copy_model_files failed, please check
2025-08-04 06:05:43.883 | INFO | PID:283 | actor_proxy_local.before_run:663 - predict policy_name: train_one, start success at pid 283, on-policy/off-policy is off-policy, actor_receive_cost_time_ms: 1, predict_batch_size: 1
2025-08-04 06:05:43.890 | INFO | PID:283 | actor_proxy_local.predict_stat:482 - aisrv predict now predict count is 0
2025-08-04 06:05:43.905 | INFO | PID:283 | predict_common.standard_load_model_detail:319 - aisrv predict had not get pull model file, so return
2025-08-04 06:05:43.945 | INFO | PID:283 | predict_common.standard_load_model_detail:319 - aisrv predict had not get pull model file, so return
2025-08-04 06:05:43.945 | ERROR | PID:308 | train_workflow.run_episodes:269 - aisrv run_episodes error: 'list' object has no attribute 'action'
2025-08-04 06:05:43.957 | ERROR | PID:308 | train_workflow.workflow:88 - aisrv workflow error: run_episodes error
2025-08-04 06:05:43.965 | ERROR | PID:307 | train_workflow.run_episodes:269 - aisrv run_episodes error: 'list' object has no attribute 'action'
2025-08-04 06:05:43.968 | ERROR | PID:307 | train_workflow.workflow:88 - aisrv workflow error: run_episodes error
2025-08-04 06:05:43.970 | ERROR | PID:307 | kaiwu_rl_helper_standard.workflow:493 - aisrv kaiwu_rl_helper workflow() RuntimeError Exception, 
Traceback (most recent call last):

  File "/data/projects/back_to_the_realm_v2/agent_ppo/workflow/train_workflow.py", line 141, in run_episodes
    act = agent.action_process(act_data[0])
          │     │              └ ([<kaiwu_agent.utils.common_func.ActData object at 0x7c3a22df1b90>], -1)
          │     └ <function Agent.action_process at 0x7c3960a3d620>
          └ <agent_ppo.agent.Agent object at 0x7c39613d7ed0>

  File "/data/projects/back_to_the_realm_v2/agent_ppo/agent.py", line 139, in action_process
    return act_data.action
           └ [<kaiwu_agent.utils.common_func.ActData object at 0x7c3a22df1b90>]

AttributeError: 'list' object has no attribute 'action'


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "/data/projects/back_to_the_realm_v2/agent_ppo/workflow/train_workflow.py", line 48, in workflow
    for g_data, monitor_data in run_episodes(episode_num_every_epoch, env, agent, usr_conf, logger, monitor):
                │               │            │                        │    │      │         │       └ <MonitorProxy name='MonitorProxy-5' pid=286 parent=264 unknown>
                │               │            │                        │    │      │         └ <kaiwudrl.common.logging.kaiwu_logger.KaiwuLogger object at 0x7c3961e96390>
                │               │            │                        │    │      └ {'env_conf': {'map_name': 'map_cherry', 'map_cherry': {'start': [111, 83], 'end': [20, 49], 'buff': [57, 68], 'start_random':...
                │               │            │                        │    └ <agent_ppo.agent.Agent object at 0x7c39613d7ed0>
                │               │            │                        └ <kaiwu_env.env.env_proxy_lazy.EnvProxy object at 0x7c3961434250>
                │               │            └ 1
                │               └ <function run_episodes at 0x7c39fa5c4400>
                └ {}

  File "/data/projects/back_to_the_realm_v2/agent_ppo/workflow/train_workflow.py", line 270, in run_episodes
    raise RuntimeError(f"run_episodes error")

RuntimeError: run_episodes error


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "/usr/lib64/python3.11/threading.py", line 1002, in _bootstrap
    self._bootstrap_inner()
    │    └ <function Thread._bootstrap_inner at 0x7c3a2a804fe0>
    └ <KaiWuRLStandardHelper(kaiwu_rl_helper_1, started daemon 136588581934848)>
  File "/usr/lib64/python3.11/threading.py", line 1045, in _bootstrap_inner
    self.run()
    │    └ <function KaiWuRLStandardHelper.run at 0x7c39fa405760>
    └ <KaiWuRLStandardHelper(kaiwu_rl_helper_1, started daemon 136588581934848)>

  File "/data/projects/back_to_the_realm_v2/kaiwudrl/server/aisrv/kaiwu_rl_helper_standard.py", line 524, in run
    self.workflow()
    │    └ <function KaiWuRLStandardHelper.workflow at 0x7c39fa4056c0>
    └ <KaiWuRLStandardHelper(kaiwu_rl_helper_1, started daemon 136588581934848)>

> File "/data/projects/back_to_the_realm_v2/kaiwudrl/server/aisrv/kaiwu_rl_helper_standard.py", line 473, in workflow
    AlgoConf[CONFIG.algo].train_workflow([env], self.current_models, self.logger, self.monitor_proxy)
    │        │      │                     │     │    │               │    │       │    └ <MonitorProxy name='MonitorProxy-5' pid=286 parent=264 unknown>
    │        │      │                     │     │    │               │    │       └ <KaiWuRLStandardHelper(kaiwu_rl_helper_1, started daemon 136588581934848)>
    │        │      │                     │     │    │               │    └ <member 'logger' of 'KaiWuRLStandardHelper' objects>
    │        │      │                     │     │    │               └ <KaiWuRLStandardHelper(kaiwu_rl_helper_1, started daemon 136588581934848)>
    │        │      │                     │     │    └ [<agent_ppo.agent.Agent object at 0x7c39613d7ed0>]
    │        │      │                     │     └ <KaiWuRLStandardHelper(kaiwu_rl_helper_1, started daemon 136588581934848)>
    │        │      │                     └ <kaiwu_env.env.env_proxy_lazy.EnvProxy object at 02025-08-04 06:05:44.498 | INFO | PID:285 | env_proxy_lazy.reset:100 - aisrv EnvProxy reset one game success
2025-08-04 06:05:44.506 | INFO | PID:281 | predict_common.standard_load_model_detail:319 - aisrv predict had not get pull model file, so return
2025-08-04 06:05:44.545 | INFO | PID:281 | predict_common.standard_load_model_detail:319 - aisrv predict had not get pull model file, so return
2025-08-04 06:05:44.545 | ERROR | PID:285 | train_workflow.run_episodes:269 - aisrv run_episodes error: 'list' object has no attribute 'action'
2025-08-04 06:05:44.546 | ERROR | PID:285 | train_workflow.workflow:88 - aisrv workflow error: run_episodes error
2025-08-04 06:05:44.547 | ERROR | PID:285 | kaiwu_rl_helper_standard.workflow:493 - aisrv kaiwu_rl_helper workflow() RuntimeError Exception, 
Traceback (most recent call last):

  File "/data/projects/back_to_the_realm_v2/agent_ppo/workflow/train_workflow.py", line 141, in run_episodes
    act = agent.action_process(act_data[0])
          │     │              └ ([<kaiwu_agent.utils.common_func.ActData object at 0x734a01eadf10>], -1)
          │     └ <function Agent.action_process at 0x734a01e3d4e0>
          └ <agent_ppo.agent.Agent object at 0x73493fadfed0>

  File "/data/projects/back_to_the_realm_v2/agent_ppo/agent.py", line 139, in action_process
    return act_data.action
           └ [<kaiwu_agent.utils.common_func.ActData object at 0x734a01eadf10>]

AttributeError: 'list' object has no attribute 'action'


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "/data/projects/back_to_the_realm_v2/agent_ppo/workflow/train_workflow.py", line 48, in workflow
    for g_data, monitor_data in run_episodes(episode_num_every_epoch, env, agent, usr_conf, logger, monitor):
                │               │            │                        │    │      │         │       └ <MonitorProxy name='MonitorProxy-5' pid=284 parent=262 unknown>
                │               │            │                        │    │      │         └ <kaiwudrl.common.logging.kaiwu_logger.KaiwuLogger object at 0x734940b03ad0>
                │               │            │                        │    │      └ {'env_conf': {'map_name': 'map_cherry', 'map_cherry': {'start': [111, 83], 'end': [20, 49], 'buff': [57, 68], 'start_random':...
                │               │            │                        │    └ <agent_ppo.agent.Agent object at 0x73493fadfed0>
                │               │            │                        └ <kaiwu_env.env.env_proxy_lazy.EnvProxy object at 0x734940565a10>
                │               │            └ 1
                │               └ <function run_episodes at 0x7349d9680400>
                └ {}

  File "/data/projects/back_to_the_realm_v2/agent_ppo/workflow/train_workflow.py", line 270, in run_episodes
    raise RuntimeError(f"run_episodes error")

RuntimeError: run_episodes error


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "/usr/lib64/python3.11/threading.py", line 1002, in _bootstrap
    self._bootstrap_inner()
    │    └ <function Thread._bootstrap_inner at 0x734a098c0fe0>
    └ <KaiWuRLStandardHelper(kaiwu_rl_helper_0, started daemon 126761143883520)>
  File "/usr/lib64/python3.11/threading.py", line 1045, in _bootstrap_inner
    self.run()
    │    └ <function KaiWuRLStandardHelper.run at 0x7349d94c1760>
    └ <KaiWuRLStandardHelper(kaiwu_rl_helper_0, started daemon 126761143883520)>

  File "/data/projects/back_to_the_realm_v2/kaiwudrl/server/aisrv/kaiwu_rl_helper_standard.py", line 524, in run
    self.workflow()
    │    └ <function KaiWuRLStandardHelper.workflow at 0x7349d94c16c0>
    └ <KaiWuRLStandardHelper(kaiwu_rl_helper_0, started daemon 126761143883520)>

> File "/data/projects/back_to_the_realm_v2/kaiwudrl/server/aisrv/kaiwu_rl_helper_standard.py", line 473, in workflow
    AlgoConf[CONFIG.algo].train_workflow([env], self.current_models, self.logger, self.monitor_proxy)
    │        │      │                     │     │    │               │    │       │    └ <MonitorProxy name='MonitorProxy-5' pid=284 parent=262 unknown>
    │        │      │                     │     │    │               │    │       └ <KaiWuRLStandardHelper(kaiwu_rl_helper_0, started daemon 126761143883520)>
    │        │      │                     │     │    │               │    └ <member 'logger' of 'KaiWuRLStandardHelper' objects>
    │        │      │                     │     │    │               └ <KaiWuRLStandardHelper(kaiwu_rl_helper_0, started daemon 126761143883520)>
    │        │      │                     │     │    └ [<agent_ppo.agent.Agent object at 0x73493fadfed0>]
    │        │      │                     │     └ <KaiWuRLStandardHelper(kaiwu_rl_helper_0, started daemon 126761143883520)>
    │        │      │                     └ <kaiwu_env.env.env_proxy_lazy.EnvProxy object at 0x734940565a10>
    │        │      └ 'ppo'
    │        └ <kaiwudrl.common.config.config_control.ConfigControl object at 0x734a0bb929d0>
    └ <kaiwudrl.common.config.algo_conf._AlgoConf object at 0x734a071f8e50>

  File "/data/projects/back_to_the_realm_v2/agent_ppo/workflow/train_workflow.py", line 89, in workflow
    raise RuntimeError(f"workflow error")

RuntimeError: workflow error
2025-08-04 06:05:44.566 | INFO | PID:285 | kaiwu_rl_helper_standard.workflow:506 - aisrv kaiwu_rl_helper aisrv send process_stop_request to learner success
2025-08-04 06:05:44.567 | INFO | PID:285 | kaiwu_rl_helper_standard.workflow:508 - aisrv kaiwu_rl_helper finally
2025-08-04 06:05:44.582 | ERROR | PID:293 | train_workflow.run_episodes:269 - aisrv run_episodes error: 'list' object has no attribute 'action'
2025-08-04 06:05:44.590 | ERROR | PID:293 | train_workflow.workflow:88 - aisrv workflow error: run_episodes error
2025-08-04 06:05:44.590 | INFO | PID:281 | predict_common.standard_load_model_detail:319 - aisrv predict had not get pull model file, so return
2025-08-04 06:05:44.591 | ERROR | PID:293 | kaiwu_rl_helper_standard.workflow:493 - aisrv kaiwu_rl_helper workflow() RuntimeError Exception, 
Traceback (most recent call last):

  File "/data/projects/back_to_the_realm_v2/agent_ppo/workflow/train_workflow.py", line 141, in run_episodes
    act = agent.action_process(act_data[0])
          │     │              └ ([<kaiwu_agent.utils.common_func.ActData object at 0x734a01ead890>], -1)
          │     └ <function Agent.action_process at 0x734a01e3d620>
          └ <agent_ppo.agent.Agent object at 0x734940396bd0>

  File "/data/projects/back_to_the_realm_v2/agent_ppo/agent.py", line 139, in action_process
    return act_data.action
           └ [<kaiwu_agent.utils.common_func.ActData object at 0x734a01ead890>]

AttributeError: 'list' object has no attribute 'action'


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "/data/projects/back_to_the_realm_v2/agent_ppo/workflow/train_workflow.py", line 48, in workflow
    for g_data, monitor_data in run_episodes(episode_num_every_epoch, env, agent, usr_conf, logger, monitor):
                │               │            │                        │    │      │         │       └ <MonitorProxy name='MonitorProxy-5' pid=284 parent=262 unknown>
                │               │            │                        │    │      │         └ <kaiwudrl.common.logging.kaiwu_logger.KaiwuLogger object at 0x734940b03ad0>
                │               │            │                        │    │      └ {'env_conf': {'map_name': 'map_cherry', 'map_cherry': {'start': [111, 83], 'end': [20, 49], 'buff': [57, 68], 'start_random':...
                │               │            │                        │    └ <agent_ppo.agent.Agent object at 0x734940396bd0>
                │               │            │                        └ <kaiwu_env.env.env_proxy_lazy.EnvProxy object at 0x73493fb15950>
                │               │            └ 1
                │               └ <function run_episodes at 0x7349d9680400>
                └ {}

  File "/data/projects/back_to_the_realm_v2/agent_ppo/workflow/train_workflow.py", line 270, in run_episodes
    raise RuntimeError(f"run_episodes error")

RuntimeError: run_episodes error


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "/usr/lib64/python3.11/threading.py", line 1002, in _bootstrap
    self._bootstrap_inner()
    │    └ <function Thread._bootstrap_inner at 0x734a098c0fe0>
    └ <KaiWuRLStandardHelper(kaiwu_rl_helper_1, started daemon 126761143883520)>
  File "/usr/lib64/python3.11/threading.py", line 1045, in _bootstrap_inner
    self.run()
    │    └ <function KaiWuRLStandardHelper.run at 0x7349d94c1760>
    └ <KaiWuRLStandardHelper(kaiwu_rl_helper_1, started daemon 126761143883520)>

  File "/data/projects/back_to_the_realm_v2/kaiwudrl/server/aisrv/kaiwu_rl_helper_standard.py", line 524, in run
    self.workflow()
    │    └ <function KaiWuRLStandardHelper.workflow at 0x7349d94c16c0>
    └ <KaiWuRLStandardHelper(kaiwu_rl_helper_1, started daemon 126761143883520)>

> File "/data/projects/back_to_the_realm_v2/kaiwudrl/server/aisrv/kaiwu_rl_helper_standard.py", line 473, in workflow
    AlgoConf[CONFIG.algo].train_workflow([env], self.current_models, self.logger, self.monitor_proxy)
    │        │      │                     │     │    │               │    │       │    └ <MonitorProxy name='MonitorProxy-5' pid=284 parent=262 unknown>
    │        │      │                     │     │    │               │    │       └ <KaiWuRLStandardHelper(kaiwu_rl_helper_1, started daemon 126761143883520)>
    │        │      │                     │     │    │               │    └ <member 'logger' of 'KaiWuRLStandardHelper' objects>
    │        │      │                     │     │    │               └ <KaiWuRLStandardHelper(kaiwu_rl_helper_1, started daemon 126761143883520)>
    │        │      │                     │     │    └ [<agent_ppo.agent.Agent object at 0x734940396bd0>]
    │        │      │                     │     └ <KaiWuRLStandardHelper(kaiwu_rl_helper_1, started daemon 126761143883520)>
    │        │      │                     └ <kaiwu_env.env.env_proxy_lazy.EnvProxy object at 0x73493fb15950>
    │        │      └ 'ppo'
    │        └ <kaiwudrl.common.config.config_control.ConfigControl object at 0x734a0bb929d0>
    └ <kaiwudrl.common.config.algo_conf._AlgoConf object at 0x734a071f8e50>

  File "/data/projects/back_to_t2025-08-04 06:05:44.653 | INFO | PID:283 | predict_common.standard_load_model_detail:319 - aisrv predict had not get pull model file, so return
2025-08-04 06:05:44.658 | ERROR | PID:311 | train_workflow.run_episodes:269 - aisrv run_episodes error: 'list' object has no attribute 'action'
2025-08-04 06:05:44.659 | ERROR | PID:311 | train_workflow.workflow:88 - aisrv workflow error: run_episodes error
2025-08-04 06:05:44.660 | ERROR | PID:311 | kaiwu_rl_helper_standard.workflow:493 - aisrv kaiwu_rl_helper workflow() RuntimeError Exception, 
Traceback (most recent call last):

  File "/data/projects/back_to_the_realm_v2/agent_ppo/workflow/train_workflow.py", line 141, in run_episodes
    act = agent.action_process(act_data[0])
          │     │              └ ([<kaiwu_agent.utils.common_func.ActData object at 0x7c3a22df12d0>], -1)
          │     └ <function Agent.action_process at 0x7c3a22d994e0>
          └ <agent_ppo.agent.Agent object at 0x7c39613d7ed0>

  File "/data/projects/back_to_the_realm_v2/agent_ppo/agent.py", line 139, in action_process
    return act_data.action
           └ [<kaiwu_agent.utils.common_func.ActData object at 0x7c3a22df12d0>]

AttributeError: 'list' object has no attribute 'action'


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "/data/projects/back_to_the_realm_v2/agent_ppo/workflow/train_workflow.py", line 48, in workflow
    for g_data, monitor_data in run_episodes(episode_num_every_epoch, env, agent, usr_conf, logger, monitor):
                │               │            │                        │    │      │         │       └ <MonitorProxy name='MonitorProxy-5' pid=286 parent=264 unknown>
                │               │            │                        │    │      │         └ <kaiwudrl.common.logging.kaiwu_logger.KaiwuLogger object at 0x7c3961e96390>
                │               │            │                        │    │      └ {'env_conf': {'map_name': 'map_cherry', 'map_cherry': {'start': [111, 83], 'end': [20, 49], 'buff': [57, 68], 'start_random':...
                │               │            │                        │    └ <agent_ppo.agent.Agent object at 0x7c39613d7ed0>
                │               │            │                        └ <kaiwu_env.env.env_proxy_lazy.EnvProxy object at 0x7c396170ecd0>
                │               │            └ 1
                │               └ <function run_episodes at 0x7c39fa5c4400>
                └ {}

  File "/data/projects/back_to_the_realm_v2/agent_ppo/workflow/train_workflow.py", line 270, in run_episodes
    raise RuntimeError(f"run_episodes error")

RuntimeError: run_episodes error


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "/usr/lib64/python3.11/threading.py", line 1002, in _bootstrap
    self._bootstrap_inner()
    │    └ <function Thread._bootstrap_inner at 0x7c3a2a804fe0>
    └ <KaiWuRLStandardHelper(kaiwu_rl_helper_3, started daemon 136588581934848)>
  File "/usr/lib64/python3.11/threading.py", line 1045, in _bootstrap_inner
    self.run()
    │    └ <function KaiWuRLStandardHelper.run at 0x7c39fa405760>
    └ <KaiWuRLStandardHelper(kaiwu_rl_helper_3, started daemon 136588581934848)>

  File "/data/projects/back_to_the_realm_v2/kaiwudrl/server/aisrv/kaiwu_rl_helper_standard.py", line 524, in run
    self.workflow()
    │    └ <function KaiWuRLStandardHelper.workflow at 0x7c39fa4056c0>
    └ <KaiWuRLStandardHelper(kaiwu_rl_helper_3, started daemon 136588581934848)>

> File "/data/projects/back_to_the_realm_v2/kaiwudrl/server/aisrv/kaiwu_rl_helper_standard.py", line 473, in workflow
    AlgoConf[CONFIG.algo].train_workflow([env], self.current_models, self.logger, self.monitor_proxy)
    │        │      │                     │     │    │               │    │       │    └ <MonitorProxy name='MonitorProxy-5' pid=286 parent=264 unknown>
    │        │      │                     │     │    │               │    │       └ <KaiWuRLStandardHelper(kaiwu_rl_helper_3, started daemon 136588581934848)>
    │        │      │                     │     │    │               │    └ <member 'logger' of 'KaiWuRLStandardHelper' objects>
    │        │      │                     │     │    │               └ <KaiWuRLStandardHelper(kaiwu_rl_helper_3, started daemon 136588581934848)>
    │        │      │                     │     │    └ [<agent_ppo.agent.Agent object at 0x7c39613d7ed0>]
    │        │      │                     │     └ <KaiWuRLStandardHelper(kaiwu_rl_helper_3, started daemon 136588581934848)>
    │        │      │                     └ <kaiwu_env.env.env_proxy_lazy.EnvProxy object at 0x7c396170ecd0>
    │        │      └ 'ppo'
    │        └ <kaiwudrl.common.config.config_control.ConfigControl object at 0x7c3a2cad69d0>
    └ <kaiwudrl.common.config.algo_conf._AlgoConf object at 0x7c3a2813ce50>

  File "/data/projects/back_to_the_realm_v2/agent_ppo/workflow/train_workflow.py", line 89, in workflow
    raise RuntimeError(f"workflow error")

RuntimeError: workflow error
2025-08-04 06:05:44.969641: W tensorflow/compiler/tf2tensorrt/utils/py_utils.cc:38] TF-TRT Warning: Could not find TensorRT
2025-08-04 06:05:46.645 | INFO | PID:285 | reverb_util.__init__:22 - aisrv learner_proxy reverb client Client, server_address=***********:9999 ***********:9999 connect to reverb server
2025-08-04 06:05:46.648 | INFO | PID:285 | learner_proxy.before_run:118 - learner_proxy send reverb server use reverb, tables is ['reverb_replay_buffer_table_0']
2025-08-04 06:05:46.654 | INFO | PID:285 | learner_proxy.before_run:155 - learner_proxy zmq client connect at ***********:9997 with client_id 20802771
2025-08-04 06:05:46.681 | INFO | PID:285 | learner_proxy.before_run:174 - learner_proxy policy_name: train_one, start success at pid 285
2025-08-04 06:05:46.715 | INFO | PID:285 | learner_proxy.run_once:276 - learner_proxy send process_stop data to learner
2025-08-04 06:05:46.720 | INFO | PID:425 | monitor_proxy_process.before_run:73 - aisrv monitor_proxy process start success at pid is 425
2025-08-04 06:05:46.916 | INFO | PID:285 | learner_proxy.run_once:287 - learner_proxy recv process_stop data result from learner success
2025-08-04 06:05:46.917 | INFO | PID:285 | learner_proxy.run_once:276 - learner_proxy send process_stop data to learner
2025-08-04 06:05:46.933 | INFO | PID:285 | learner_proxy.run_once:287 - learner_proxy recv process_stop data result from learner success
2025-08-04 06:05:46.935 | INFO | PID:285 | learner_proxy.run_once:276 - learner_proxy send process_stop data to learner
2025-08-04 06:05:46.950 | INFO | PID:285 | learner_proxy.run_once:287 - learner_proxy recv process_stop data result from learner success
2025-08-04 06:05:46.953 | INFO | PID:285 | learner_proxy.run_once:276 - learner_proxy send process_stop data to learner
2025-08-04 06:05:46.971 | INFO | PID:285 | learner_proxy.run_once:287 - learner_proxy recv process_stop data result from learner success
