{"time": "2025-08-04 00:45:53.350282", "level": "INFO", "message": "aisrv AiServer AiSrvHandle with address: **********:5566 start", "file": "aisrv_server_standard.py", "line": "484", "module": "aisrv", "process": "aisrv_server_standard", "function": "start_aisrv_handler", "stack": "", "pid": 8709}
{"time": "2025-08-04 00:45:53.355934", "level": "INFO", "message": "aisrv AiServer is start success at 0.0.0.0:8000, pid is 8709, run_mode is train, self_play is False", "file": "aisrv_server_standard.py", "line": "604", "module": "aisrv", "process": "aisrv_server_standard", "function": "before_run", "stack": "", "pid": 8709}
