{"time": "2025-08-04 00:40:22.950237", "level": "INFO", "message": "learner train process start at pid is 5218", "file": "trainer.py", "line": "32", "module": "learner", "process": "trainer", "function": "__init__", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:22.990674", "level": "INFO", "message": "learner train replaybuff, use reverb", "file": "replay_buffer_wrapper.py", "line": "58", "module": "learner", "process": "replay_buffer_wrapper", "function": "__init__", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:23.001811", "level": "INFO", "message": "learner model_file_save process start, type is 0, no need get mode file from cos", "file": "model_file_save.py", "line": "776", "module": "learner", "process": "model_file_save", "function": "start_actor_process_by_type", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:23.389751", "level": "INFO", "message": "learner monitor_proxy process start success at pid is 5348", "file": "monitor_proxy_process.py", "line": "73", "module": "learner", "process": "monitor_proxy_process", "function": "before_run", "stack": "", "pid": 5348}
{"time": "2025-08-04 00:40:24.050642", "level": "INFO", "message": "learner policy_name train_one, algo ppo, model_wrapper is StandardModelWrapperPytorch", "file": "model_wrapper_common.py", "line": "99", "module": "learner", "process": "model_wrapper_common", "function": "create_standard_model_wrapper", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:24.127472", "level": "INFO", "message": "learner train zmq server on learner bind at 0.0.0.0:9997 for aisrv", "file": "on_policy_trainer.py", "line": "691", "module": "learner", "process": "on_policy_trainer", "function": "before_run", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:24.147123", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-0.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:24.216368", "level": "INFO", "message": "model_file_sync push output_file_name /data/ckpt/back_to_the_realm_v2_ppo/kaiwu_checkpoint_back_to_the_realm_v2_ppo_0.tar.gz key model.ckpt_back_to_the_realm_v2_ppo_0 to modelpool success,             total push to modelpool succ cnt is 1             total push to modelpool err cnt is 0", "file": "model_file_sync.py", "line": "560", "module": "learner", "process": "model_file_sync", "function": "push_checkpoint_to_model_pool", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:24.217209", "level": "INFO", "message": "learner train first model file push to modelpool success", "file": "on_policy_trainer.py", "line": "723", "module": "learner", "process": "on_policy_trainer", "function": "before_run", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:24.221948", "level": "INFO", "message": "learner train process start success at 5218, on-policy/off-policy is off-policy, ppo trainer global step -1.0, load app back_to_the_realm_v2 algo ppo model, train_batch_size is 2", "file": "on_policy_trainer.py", "line": "756", "module": "learner", "process": "on_policy_trainer", "function": "before_run", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:24.235009", "level": "INFO", "message": "learner start_background_filler success, reverb.Client connect at localhost:9999", "file": "reverb_dataset_v1.py", "line": "57", "module": "learner", "process": "reverb_dataset_v1", "function": "start_background_filler", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:24.455078", "level": "INFO", "message": "learner model_file_sync, use modelpool, use_which_deep_learning_framework is pytorch", "file": "model_file_sync.py", "line": "189", "module": "learner", "process": "model_file_sync", "function": "before_run", "stack": "", "pid": 5389}
{"time": "2025-08-04 00:40:24.459574", "level": "INFO", "message": "model_file_sync process pid is 5389", "file": "model_file_sync.py", "line": "195", "module": "learner", "process": "model_file_sync", "function": "before_run", "stack": "", "pid": 5389}
{"time": "2025-08-04 00:40:24.461174", "level": "INFO", "message": "model_file_sync mkdir /data/ckpt/back_to_the_realm_v2_ppo/convert_models_learner success", "file": "model_file_sync.py", "line": "153", "module": "learner", "process": "model_file_sync", "function": "make_model_dirs", "stack": "", "pid": 5389}
{"time": "2025-08-04 00:40:24.466377", "level": "INFO", "message": "learner ppid is 131362738833216", "file": "monitor_proxy.py", "line": "36", "module": "learner", "process": "monitor_proxy", "function": "__init__", "stack": "", "pid": 5389}
{"time": "2025-08-04 00:40:24.858151", "level": "INFO", "message": "learner ppid is 131362738833216", "file": "monitor_proxy.py", "line": "36", "module": "learner", "process": "monitor_proxy", "function": "__init__", "stack": "", "pid": 5394}
{"time": "2025-08-04 00:40:24.861280", "level": "INFO", "message": "model_file_save process start success at pid 5394", "file": "model_file_save.py", "line": "300", "module": "learner", "process": "model_file_save", "function": "before_run", "stack": "", "pid": 5394}
{"time": "2025-08-04 00:40:38.001014", "level": "INFO", "message": "learner train_step is 0, so not save_model", "file": "standard_model_wrapper_pytorch.py", "line": "238", "module": "learner", "process": "standard_model_wrapper_pytorch", "function": "save_param_detail", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:38.002378", "level": "INFO", "message": "learner train learner save_param_by_framework is success, ip is 172.20.0.5_0", "file": "on_policy_trainer.py", "line": "974", "module": "learner", "process": "on_policy_trainer", "function": "save_model_detail", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:38.003340", "level": "INFO", "message": "learner train learner really save_model from aisrv 172.20.0.5_0", "file": "on_policy_trainer.py", "line": "1055", "module": "learner", "process": "on_policy_trainer", "function": "learner_process_message_by_aisrv", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:38.590788", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-1.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:38.607234", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-2.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:38.629969", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-3.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:38.643381", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-4.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:38.657658", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-5.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:38.672238", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-6.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:38.688289", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-7.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:38.702091", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-8.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:38.715203", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-9.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:38.732633", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-10.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:38.749147", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-11.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:38.765903", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-12.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:38.781991", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-13.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:38.797756", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-14.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:38.815840", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-15.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:38.830512", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-16.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:38.847712", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-17.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:38.860256", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-18.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:38.875270", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-19.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:38.887350", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-20.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:38.907855", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-21.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:38.925091", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-22.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:38.939953", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-23.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:38.965661", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-24.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:38.990874", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-25.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:39.009386", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-26.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:39.038619", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-27.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:39.062212", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-28.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:39.082208", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-29.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:39.103397", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-30.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:39.124957", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-31.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:39.150489", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-32.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:39.174891", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-33.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:39.194058", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-34.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:39.209453", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-35.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:39.228776", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-36.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:39.248560", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-37.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:39.262799", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-38.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:39.280575", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-39.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:39.299551", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-40.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:39.316527", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-41.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:39.334365", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-42.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:39.353235", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-43.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:39.371458", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-44.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:39.395855", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-45.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:39.416522", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-46.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
{"time": "2025-08-04 00:40:39.442022", "level": "INFO", "message": "learner save model /data/ckpt/back_to_the_realm_v2_ppo/model.ckpt-47.pkl successfully", "file": "agent.py", "line": "122", "module": "learner", "process": "agent", "function": "save_model", "stack": "", "pid": 5218}
